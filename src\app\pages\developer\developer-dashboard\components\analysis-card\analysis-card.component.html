<div class="card card-flush bg-{{ backgroundColor }}">
  <div class="card-header border-0 d-flex justify-content-start">
    <span class="badge badge-light-{{ backgroundColor }} fw-bold me-2 fs-base">
      {{ title }}
    </span>
  </div>

  <div class="card-body p-0 d-flex flex-column">
    <div class="card-p bg-body flex-grow-1 bg-{{ backgroundColor }}">
      <div class="row g-0">
        <div class="col text-start">
          <div class="fs-7 fw-bold text-white">Count</div>
          <div class="fs-4 fw-bolder text-white">
            {{ activeRequests }}
          </div>
        </div>
        <div class="col text-end">
          <div class="fs-7 fw-bold text-white">NumberOfProjects</div>
          <div class="fs-4 fw-bolder text-white">
            {{ totalRequests }}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>