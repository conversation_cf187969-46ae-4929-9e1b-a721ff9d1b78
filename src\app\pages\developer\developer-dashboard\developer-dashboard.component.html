<div class="dashboard-container">
  <app-developer-header></app-developer-header>

  <div class="dashboard-content">
    <!-- Dashboard Content -->
    <div class="dashboard-cards-container">
      <!-- Project Statistics Cards -->
      <div class="row g-3 mb-4">
        <div class="col-xl-3">
          <div class="analysis-card-item">
            <app-analysis-card [backgroundColor]="'primary'" [title]="'Total Projects'" [totalRequests]="25"
              [activeRequests]="8">
            </app-analysis-card>
          </div>
        </div>
        <div class="col-xl-3">
          <div class="analysis-card-item">
            <app-analysis-card [backgroundColor]="'success'" [title]="'Completed Projects'" [totalRequests]="17"
              [activeRequests]="0">
            </app-analysis-card>
          </div>
        </div>
        <div class="col-xl-4">
          <div class="analysis-card-item">
            <app-analysis-card [backgroundColor]="'warning'" [title]="'Active Projects'" [totalRequests]="8"
              [activeRequests]="8">
            </app-analysis-card>
          </div>
        </div>
      </div>



      <!-- Analysis Cards and Pie Chart Section -->
      <div class="main-dashboard-container">
        <!-- Analysis Cards - 2x2 Grid -->
        <div class="analysis-cards-container">
          <div class="analysis-card-item">
            <app-analysis-card [backgroundColor]="analysisCards[0].backgroundColor" [title]="analysisCards[0].title"
              [totalRequests]="analysisCards[0].totalRequests"
              [activeRequests]="analysisCards[0].activeRequests"></app-analysis-card>
          </div>
          <div class="analysis-card-item">
            <app-analysis-card [backgroundColor]="analysisCards[1].backgroundColor" [title]="analysisCards[1].title"
              [totalRequests]="analysisCards[1].totalRequests"
              [activeRequests]="analysisCards[1].activeRequests"></app-analysis-card>
          </div>
          <div class="analysis-card-item mt-2">
            <app-analysis-card [backgroundColor]="analysisCards[2].backgroundColor" [title]="analysisCards[2].title"
              [totalRequests]="analysisCards[2].totalRequests"
              [activeRequests]="analysisCards[2].activeRequests"></app-analysis-card>
          </div>
          <div class="analysis-card-item mt-2">
            <app-analysis-card [backgroundColor]="analysisCards[3].backgroundColor" [title]="analysisCards[3].title"
              [totalRequests]="analysisCards[3].totalRequests"
              [activeRequests]="analysisCards[3].activeRequests"></app-analysis-card>
          </div>
        </div>

        <!-- Pie Chart - Side by Side with Cards -->
        <div class="dashboard-pie-chart">
          <app-project-pie-chart [newCount]="unitStats.new" [availableCount]="unitStats.available"
            [soldCount]="unitStats.sold" [reservedCount]="unitStats.reserved" [title]="'Unit Statistics'"
            [subtitle]="'Units distribution overview'">
          </app-project-pie-chart>
        </div>
      </div>

      <!-- Charts Section -->
      <!-- <div class="row g-5 g-xl-8 mt-5">

        <div class="col-xl-6">
          <app-chart chartType="compound"></app-chart>
        </div>

        <div class="col-xl-6">
          <app-chart chartType="non-compound"></app-chart>
        </div>
      </div> -->

      <!-- Monthly contract requests -->
      <div class="row g-5 g-xl-8 mt-5">
        <div class="col-xl-12">
          <div class="card card-xl-stretch mb-1 mb-xl-4">
            <div class="card-header border-0 d-flex justify-content-between">
              <div class="project-title-left d-flex align-items-center">
                <i class="fa-solid fa-chart-line me-2 text-primary"></i>
                <span class="text-dark fw-bold">Monthly Contract Requests</span>
              </div>
            </div>
            <div class="card-body">
              <div class="tab-content">
                <div class="tab-pane fade show active" id="kt_tab_pane_1">
                  <div class="d-flex flex-wrap flex-stack">
                    <div class="d-flex flex-column flex-grow-1 pe-8">
                      <div class="table-responsive">
                        <table class="table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3">
                          <thead>
                            <tr class="fw-bolder bg-light-dark-blue text-dark-blue me-1 ms-1">
                              <th class="min-w-120px ps-4 rounded-start"> Broker Name</th>
                              <th class="min-w-120px ps-4 rounded-start">Contact info</th>
                              <th class="min-w-120px"> Request Date</th>
                              <th class="min-w-120px rounded-end">Status</th>
                            </tr>
                          </thead>

                          <tbody>
                            <!-- Debug info -->
                            <tr *ngIf="newRequests?.length === 0">
                              <td colspan="4" class="text-center text-muted">
                                No data available
                              </td>
                            </tr>

                            <!-- Data Rows -->
                            <tr *ngFor="let request of newRequests">
                              <td>
                                <span class="text-gray-800 fw-semibold d-block mb-1 fs-6">
                                  {{ request?.broker?.fullName || ' undefined ' }}
                                </span>
                              </td>
                              <td>
                                <div class="text-gray-800 fw-semibold d-block mb-1 fs-6">
                                  <div>{{ request?.broker?.email || ' undefined ' }}</div>
                                  <div class="text-muted fs-7">{{ request?.broker?.phone || ' undefined ' }}</div>
                                </div>
                              </td>
                              <td>
                                <span class="text-gray-800 fw-semibold d-block mb-1 fs-6">
                                  {{ request?.contract?.createdAt || ' undefined ' }}
                                </span>
                              </td>
                              <td>
                                <span class="badge" [ngClass]="{
                                        'badge-light-warning': request?.contract?.status === 'pending',
                                        'badge-light-success': request?.contract?.status === 'accepted',
                                        'badge-light-danger': request?.contract?.status === 'rejected'
                                      }">
                                  {{ request?.contract?.status || ' undefined ' }}
                                </span>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                  <div id="kt_charts_widget_1_chart" style="height: 200px"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>