import { ChangeDetectorRef, Component } from '@angular/core';
import { DeveloperService } from '../services/developer.service';

@Component({
  selector: 'app-developer-dashboard',
  templateUrl: './developer-dashboard.component.html',
  styleUrl: './developer-dashboard.component.scss',
})
export class DeveloperDashboardComponent {
  // Project statistics
  projectStats = [
    { title: 'Project Count', totalValue: 50, activeValue: 50, change: '0.00' },
    { title: 'Project Count', totalValue: 50, activeValue: 50, change: '0.00' },

  ];

  // Analysis card data
  analysisCards = [
    {
      backgroundColor: 'primary',
      title: 'New Projects',
      totalRequests: 50,
      activeRequests: 0,
    },
    {
      backgroundColor: 'success',
      title: 'Completed Projects',
      totalRequests: 50,
      activeRequests: 0,
    },
    {
      backgroundColor: 'warning',
      title: 'In Progress Projects',
      totalRequests: 50,
      activeRequests: 0,
    },
    {
      backgroundColor: 'danger',
      title: 'Delayed Projects',
      totalRequests: 50,
      activeRequests: 0,
    },
  ];

  // Monthly statistics data (from monthly-statistics component)
  newRequests: any[] = [];

  // Pie chart data
  unitStats: any = {};

  // Contract request stats
  contractStats = {
    accepted: 0,
    pending: 3,
    declined: 0
  };


  private developerId: number = 1;

  constructor(private developerService: DeveloperService ,private cd: ChangeDetectorRef

  ) {


  }

  ngOnInit() {
    this.loadStatistics();
    this.loadPieChartStatistics();
  }

  loadStatistics() {
    this.developerService.getDeveloperStatistics(this.developerId).subscribe({
        next: (response) => {
          console.log('Statistics response:', response.data);
          this.newRequests =  response.data ;
          this.cd.detectChanges();
        },
        error: (error) => {
          console.error('Error loading statistics:', error);
          this.newRequests = [];
        }
      });
  }

  loadPieChartStatistics() {
    this.developerService.getDeveloperPieChartStatistics(this.developerId).subscribe({
      next: (response) => {
        console.log('Pie Chart Statistics response:', response);
        if (response.data && response.data.UnitStats) {
          this.unitStats = response.data.UnitStats;
          this.cd.detectChanges();
        }
      },
      error: (error) => {
        console.error('Error loading pie chart statistics:', error);
      }
    });
  }



}
