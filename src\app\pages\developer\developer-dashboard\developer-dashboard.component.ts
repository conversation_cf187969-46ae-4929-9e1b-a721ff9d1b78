import { ChangeDetectorRef, Component } from '@angular/core';
import { DeveloperService } from '../services/developer.service';

@Component({
  selector: 'app-developer-dashboard',
  templateUrl: './developer-dashboard.component.html',
  styleUrl: './developer-dashboard.component.scss',
})
export class DeveloperDashboardComponent {

  newRequests: any[] = [];

  unitStats: any = {};

  contractStats: any = {};

  // Project stats data
  projectStatsData: any = {
    apartments_count: 0,
    buildings_count: 0,
    villas_count: 0,
    duplex_count: 0,
    administrative_units_count: 0,
    commercial_units_count: 0
  };

  private developerId: number = 2;

  constructor(private developerService: DeveloperService ,private cd: ChangeDetectorRef

  ) {


  }

  ngOnInit() {
    this.loadStatistics();
    this.loadPieChartStatistics();
  }

  loadStatistics() {
    this.developerService.getDeveloperStatistics(this.developerId).subscribe({
        next: (response) => {
          console.log('Statistics response:', response.data);
          this.newRequests =  response.data ;
          this.cd.detectChanges();
        },
        error: (error) => {
          console.error('Error loading statistics:', error);
          this.newRequests = [];
        }
      });
  }

  loadPieChartStatistics() {
    this.developerService.getDeveloperPieChartStatistics(this.developerId).subscribe({
      next: (response) => {
        console.log('Pie Chart Statistics response:', response);
        if (response.data) {
          const data = response.data;

          // Load Unit Stats
          if (data.UnitStats) {
            this.unitStats = data.UnitStats;
            console.log('Unit Stats:', this.unitStats);
          }

          // Load Contract Request Stats
          if (data.ContractRequestStats) {
            this.contractStats = data.ContractRequestStats;
            console.log('Contract Stats:', this.contractStats);
          }

          // Load Project Stats
          if (data.ProjectStats) {
            this.projectStatsData = data.ProjectStats;
            console.log('Project Stats:', this.projectStatsData);
          }

          this.cd.detectChanges();
        }
      },
      error: (error) => {
        console.error('Error loading pie chart statistics:', error);
      }
    });
  }



}
